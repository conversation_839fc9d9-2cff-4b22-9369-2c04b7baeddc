import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { trigger, style, transition, animate } from '@angular/animations';
import { MessagesService } from '../../../core/services/messages.service';
import { CreateMessage } from '../../../core/models/studio-request.models';

@Component({
  selector: 'app-contact-popup',
  templateUrl: './contact-popup.component.html',
  styleUrls: ['./contact-popup.component.css'],
  standalone: false,
  animations: [
    trigger('slideUpIn', [
      transition(':enter', [
        style({
          transform: 'translateY(100%) scale(0.9)',
          opacity: 0,
          visibility: 'visible'
        }),
        animate('600ms cubic-bezier(0.34, 1.56, 0.64, 1)', style({
          transform: 'translateY(0) scale(1)',
          opacity: 1
        }))
      ]),
      transition(':leave', [
        style({
          transform: 'translateY(0) scale(1)',
          opacity: 1
        }),
        animate('350ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({
          transform: 'translateY(100%) scale(0.9)',
          opacity: 0
        }))
      ])
    ]),
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({ opacity: 1, transform: 'scale(1)' }))
      ])
    ]),
    trigger('iconAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate('150ms cubic-bezier(0.34, 1.56, 0.64, 1)', style({ opacity: 1, transform: 'scale(1)' }))
      ]),
      transition(':leave', [
        animate('250ms ease-out', style({ opacity: 0, transform: 'scale(0.8)' }))
      ])
    ])
  ]
})
export class ContactPopupComponent implements OnInit, OnDestroy {
  isVisible = true; // Icon is always visible
  isExpanded = false;
  contactForm: FormGroup;
  isSubmitting = false;
  submitMessage = '';
  submitError = '';
  hasAutoOpened = false; // Track if auto-open has happened

  private autoOpenTimer?: number;

  constructor(
    private fb: FormBuilder,
    private messagesService: MessagesService
  ) {
    this.contactForm = this.fb.group({
      phone: ['', [Validators.required, Validators.pattern(/^\+?[0-9\s\-\(\)]{10,}$/)]],
      message: ['', [Validators.required, Validators.minLength(3)]]
    });

    // Enable message field only when phone is filled
    this.contactForm.get('phone')?.valueChanges.subscribe(phoneValue => {
      const messageControl = this.contactForm.get('message');
      if (phoneValue && phoneValue.trim()) {
        messageControl?.enable();
      } else {
        messageControl?.disable();
        messageControl?.setValue('');
      }
    });

    // Initially disable message field
    this.contactForm.get('message')?.disable();
  }

  ngOnInit() {
    // Auto-open after 2 seconds, but only on desktop
    if (this.isDesktop()) {
      // Auto-open at 2 seconds (no pre-animation)
      this.autoOpenTimer = window.setTimeout(() => {
        if (!this.hasAutoOpened) {
          this.isExpanded = true;
          this.hasAutoOpened = true;
        }
      }, 4000);
    }
  }

  ngOnDestroy() {
    if (this.autoOpenTimer) {
      clearTimeout(this.autoOpenTimer);
    }
  }

  togglePopup() {
    this.isExpanded = !this.isExpanded;
    if (this.isExpanded) {
      this.resetForm();
    }
  }

  closePopup() {
    this.isExpanded = false; // Only close the expanded form, keep icon visible
  }

  private isDesktop(): boolean {
    return window.innerWidth >= 1024; // Desktop breakpoint (lg in Tailwind)
  }

  minimizePopup() {
    this.isExpanded = false;
  }

  onSubmit() {
    if (this.contactForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitMessage = '';

      const messageData: CreateMessage = {
        phone: this.contactForm.value.phone,
        message: this.contactForm.value.message
      };

      this.messagesService.createMessage(messageData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.submitMessage = 'Спасибо! Мы свяжемся с вами в ближайшее время.';
          this.contactForm.reset();

          // Re-disable message field after reset
          this.contactForm.get('message')?.disable();

          // Google Ads conversion tracking
          this.trackGoogleAdsConversion();

          // Auto close after success, but keep icon visible
          setTimeout(() => {
            this.isExpanded = false;
          }, 2000);
        },
        error: (error) => {
          this.isSubmitting = false;
          this.handleSubmitError(error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      control?.markAsTouched();
    });
  }

  private resetForm() {
    this.contactForm.reset();
    this.submitMessage = '';
    this.submitError = '';
    this.isSubmitting = false;

    // Re-disable message field after reset
    this.contactForm.get('message')?.disable();
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.contactForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно для заполнения';
      }
      if (field.errors['minlength']) {
        if (fieldName === 'message') {
          return 'Минимум 10 символов';
        }
        return 'Минимум 2 символа';
      }
      if (field.errors['pattern']) {
        return 'Введите корректный номер телефона';
      }
    }
    return '';
  }

  private handleSubmitError(error: any): void {
    console.error('Submit error:', error);

    if (error.status === 400 && error.error) {
      // Handle validation errors from backend
      const errorMessage = error.error.message || 'Ошибка валидации данных';
      this.submitError = errorMessage;
    } else if (error.status === 0) {
      // Network error
      this.submitError = 'Ошибка соединения. Проверьте подключение к интернету.';
    } else {
      // Generic error
      this.submitError = 'Произошла ошибка при отправке сообщения. Попробуйте позже.';
    }
  }

  // Google Ads conversion tracking
  private trackGoogleAdsConversion(): void {
    if (typeof (window as any).gtag === 'function') {
      (window as any).gtag('event', 'conversion', {
        'send_to': 'AW-17264732790/ZzD1CI-lxOlaEPbUu6hA'
      });
    }
  }
}
